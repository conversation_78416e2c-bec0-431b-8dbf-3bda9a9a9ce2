from unisonai import Single_Agent
from unisonai.llms.helpingaillm import HelpingAI
from unisonai.tools.websearch import WebSearchTool
from unisonai import config

# Configure API key for HelpingAI
config.set_api_key("helpingai", "hl-7d62542a-a836-4e2d-930b-32a0a72232e4")

web_agent = Single_Agent(
    llm=HelpingAI(model="Dhanishtha-2.0-preview", hide_think=False),
    identity="Web Explorer",
    description="Web Searcher for multiple queries",
    tools=[WebSearchTool],
    history_folder="history",
    output_file="output.txt"
)

web_agent.unleash(task="Dhanishta 2.0 model deatiles")